import type { Config } from "tailwindcss";

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  safelist: [
    // Classes de gradiente para o carousel
    'from-red-900',
    'from-gray-900',
    'from-black',
    'from-blue-900',
    'from-green-900',
    'from-purple-900',
    'from-yellow-900',
    'from-indigo-900',
    'from-pink-900',
    'from-orange-900',
    'to-black',
    'to-gray-900',
    'to-red-900',
    'to-blue-900',
    'to-green-900',
    'to-purple-900',
    'to-yellow-900',
    'to-indigo-900',
    'to-pink-900',
    'to-orange-900',
    'to-gray-800',
    'to-gray-700',
    'bg-gradient-to-br',
    'bg-gradient-to-r',
    'bg-gradient-to-l',
    'bg-gradient-to-t',
    'bg-gradient-to-b',
    'bg-gradient-to-tr',
    'bg-gradient-to-tl',
    'bg-gradient-to-bl',
  ],
  theme: {
  	container: {
  		center: true,
  		padding: '2rem',
  		screens: {
  			'2xl': '1400px'
  		}
  	},
  	screens: {
  		'xs': '475px',
  		'sm': '640px',
  		'md': '768px',
  		'lg': '1024px',
  		'xl': '1280px',
  		'2xl': '1536px',
  	},
  	extend: {
  		colors: {
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			primary: {
  				'50': 'hsl(var(--primary) / 0.05)',
  				'100': 'hsl(var(--primary) / 0.10)',
  				'200': 'hsl(var(--primary) / 0.20)',
  				'300': 'hsl(var(--primary) / 0.30)',
  				'400': 'hsl(var(--primary) / 0.40)',
  				'500': 'hsl(var(--primary))',
  				'600': 'hsl(var(--primary))',
  				'700': 'hsl(var(--primary))',
  				'800': 'hsl(var(--primary))',
  				'900': 'hsl(var(--primary))',
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			warning: {
  				DEFAULT: 'hsl(var(--warning))',
  				foreground: 'hsl(var(--warning-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			tenant: {
  				primary: {
  					DEFAULT: 'var(--tenant-primary)',
  					light: 'var(--tenant-primary-light)',
  					dark: 'var(--tenant-primary-dark)'
  				},
  				secondary: {
  					DEFAULT: 'var(--tenant-secondary)',
  					light: 'var(--tenant-secondary-light)',
  					dark: 'var(--tenant-secondary-dark)'
  				}
  			},
  			neutral: {
  				'25': 'hsl(210 20% 99%)',
  				'50': 'hsl(210 20% 98%)',
  				'75': 'hsl(210 16% 97%)',
  				'100': 'hsl(210 14% 96%)',
  				'150': 'hsl(210 14% 95%)',
  				'200': 'hsl(210 16% 93%)',
  				'250': 'hsl(210 16% 91%)',
  				'300': 'hsl(210 14% 89%)'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
  			fadeIn: {
  				'0%': {
  					opacity: '0',
  					transform: 'translateY(-5px)'
  				},
  				'100%': {
  					opacity: '1',
  					transform: 'translateY(0)'
  				}
  			},
  			ping: {
  				'75%, 100%': {
  					transform: 'scale(1.5)',
  					opacity: '0'
  				}
  			},
  			ripple: {
  				'0%': {
  					transform: 'scale(0)',
  					opacity: '0.4'
  				},
  				'100%': {
  					transform: 'scale(1)',
  					opacity: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
  			fadeIn: 'fadeIn 0.2s ease-out forwards',
  			'ping-slow': 'ping 2s cubic-bezier(0, 0, 0.2, 1) infinite',
  			ripple: 'ripple 0.6s linear forwards'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

export default config;
