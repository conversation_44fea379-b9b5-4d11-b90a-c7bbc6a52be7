'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Calendar, Download } from 'lucide-react';
import { GrowthMetrics } from './GrowthMetrics';
import { GrowthChart } from './GrowthChart';

export function CrescimentoContent() {
  const [viewMode, setViewMode] = useState<'year-to-date' | 'projection'>('year-to-date');

  return (
    <div className="space-y-4 sm:space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-1">
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Análise de Crescimento</h1>
            <p className="text-sm sm:text-base text-muted-foreground">
              An<PERSON><PERSON><PERSON> de crescimento da receita e métricas de expansão da academia.
            </p>
          </div>
          <div className="flex gap-2 self-start sm:self-auto">
            <Button variant="outline" size="sm" className="text-xs sm:text-sm">
              <Download className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              <span className="hidden xs:inline">Exportar</span>
              <span className="xs:hidden">Export</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Seletor de visualização */}
      <div className="flex gap-2 mb-4 sm:mb-6">
        <Button
          variant={viewMode === 'year-to-date' ? 'default' : 'outline'}
          onClick={() => setViewMode('year-to-date')}
          className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 py-2"
        >
          <span className="hidden xs:inline">Ano-a-data</span>
          <span className="xs:hidden">YTD</span>
        </Button>
        <Button
          variant={viewMode === 'projection' ? 'default' : 'outline'}
          onClick={() => setViewMode('projection')}
          className="flex-1 sm:flex-none text-xs sm:text-sm px-3 sm:px-4 py-2"
        >
          <span className="hidden xs:inline">Projeção até final do ano</span>
          <span className="xs:hidden">Projeção</span>
        </Button>
      </div>

      {/* Layout responsivo - mobile: stack vertical, desktop: lado a lado */}
      <div className="flex flex-col xl:grid xl:grid-cols-3 gap-4 sm:gap-6 xl:items-start">
        {/* Métricas de crescimento - mobile: primeiro, desktop: coluna da esquerda */}
        <div className="xl:col-span-1 order-1 xl:order-1">
          <GrowthMetrics viewMode={viewMode} />
        </div>

        {/* Gráfico de receita - mobile: segundo, desktop: coluna da direita */}
        <div className="xl:col-span-2 order-2 xl:order-2">
          <GrowthChart viewMode={viewMode} />
        </div>
      </div>
    </div>
  );
}
