'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Download, Filter } from 'lucide-react';
import { FinancialFiltersProps, FinancialFilterState, PaymentMethodOption, TransactionType, ExpenseCategory } from '../types';
import { FinancialFilterPopover } from './FinancialFilterPopover';

interface ExtendedFinancialFiltersProps extends FinancialFiltersProps {
  filters?: FinancialFilterState;
  onFilterChange?: (filters: FinancialFilterState) => void;
  onClearFilters?: () => void;
  paymentMethods?: PaymentMethodOption[];
  expenseCategories?: ExpenseCategory[];
}

export function FinancialFilters({
  onFiltersClick,
  onSearchChange,
  onExportClick,
  transactionType = 'income',
  filters = {},
  onFilterChange,
  onClearFilters,
  paymentMethods = [],
  expenseCategories = [],
}: ExtendedFinancialFiltersProps = {}) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onSearchChange?.(value);
  };

  const handleExportClick = () => {
    onExportClick?.() || console.log('Exportar dados');
  };

  const handleFilterChange = (newFilters: FinancialFilterState) => {
    onFilterChange?.(newFilters);
  };

  const getPlaceholderText = () => {
    return transactionType === 'income' ? 'Buscar por aluno' : 'Buscar por fornecedor';
  };

  const getAddButtonText = () => {
    return transactionType === 'income' ? 'Nova Receita' : 'Nova Despesa';
  };

  const getAddButtonIcon = () => {
    return transactionType === 'income' ? (
      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      </svg>
    ) : (
      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
      </svg>
    );
  };

  return (
    <Card className="mb-4 sm:mb-6 border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden">
      <CardContent className="p-4 sm:p-6 bg-gradient-to-r from-blue-50/30 to-transparent dark:from-blue-900/10 dark:to-transparent">
        <div className="flex flex-col gap-3 sm:gap-4">
          {/* Primeira linha: Filtros e Busca */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-stretch sm:items-center">
            <div className="flex-shrink-0">
              {onFilterChange ? (
                <FinancialFilterPopover
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onClearFilters={onClearFilters}
                  paymentMethods={paymentMethods}
                  transactionType={transactionType}
                  expenseCategories={expenseCategories}
                />
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onFiltersClick}
                  className="w-full sm:w-auto bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700 text-blue-700 dark:text-blue-300 hover:bg-gradient-to-r hover:from-blue-100 hover:to-blue-200 dark:hover:from-blue-900/30 dark:hover:to-blue-800/30 transition-all duration-200"
                >
                  <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-2">
                    <Filter className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                  </div>
                  Filtros
                </Button>
              )}
            </div>

            <div className="relative flex-1 min-w-0">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 bg-gray-100 dark:bg-gray-700 rounded-full">
                <Search className="h-3 w-3 text-gray-600 dark:text-gray-400" />
              </div>
              <Input
                placeholder={getPlaceholderText()}
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-12 bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 focus:border-blue-400 dark:focus:border-blue-500 transition-colors duration-200 shadow-sm"
              />
            </div>
          </div>

          {/* Segunda linha: Botões de ação */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-2 sm:justify-end">
            {transactionType === 'expense' && (
              <Link
                href="/financeiro/pagamentos/adicionar-despesa"
                className="inline-flex items-center justify-center gap-2 h-8 px-3 text-sm font-medium rounded-md border bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700 text-green-700 dark:text-green-300 hover:bg-gradient-to-r hover:from-green-100 hover:to-green-200 dark:hover:from-green-900/30 dark:hover:to-green-800/30 transition-all duration-200"
              >
                <div className="p-1 bg-green-100 dark:bg-green-900/30 rounded-full">
                  {getAddButtonIcon()}
                </div>
                <span className="hidden sm:inline">{getAddButtonText()}</span>
                <span className="sm:hidden">Nova</span>
              </Link>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleExportClick}
              disabled
              className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/20 dark:to-gray-700/20 border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400 shadow-sm"
            >
              <div className="p-1 bg-gray-100 dark:bg-gray-700 rounded-full mr-2">
                <Download className="h-3 w-3 text-gray-500 dark:text-gray-400" />
              </div>
              <span className="hidden sm:inline">Exportar</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
