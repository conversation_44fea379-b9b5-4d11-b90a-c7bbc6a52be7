'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';
import { <PERSON><PERSON>ontainer, ChartTooltip, ChartTooltipContent, type ChartConfig } from "@/components/ui/chart";
import { PieChartData } from '../types';

interface InstructorsContractChartProps {
  data: PieChartData[];
}

export function InstructorsContractChart({ data }: InstructorsContractChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="dashboard-stats-card h-auto min-h-[280px] sm:h-[320px] flex items-center justify-center">
        <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
          Sem dados disponíveis
        </p>
      </div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);

  const chartConfig: ChartConfig = Object.fromEntries(
    data.map((d) => [d.name, { label: d.name, color: d.color }])
  );

  return (
    <div className="dashboard-stats-card h-auto min-h-[280px] sm:h-[320px]">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4">
        <h3 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white">
          Tipos de Contrato - Instrutores
        </h3>
        <span className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
          Total: {total}
        </span>
      </div>

      <div className="h-[140px] sm:h-[200px] flex justify-center">
        <ChartContainer config={chartConfig} className="w-full max-w-[250px] sm:max-w-[280px] h-[140px] sm:h-[200px]">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={35}
              outerRadius={60}
              paddingAngle={2}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <ChartTooltip content={<ChartTooltipContent />} />
          </PieChart>
        </ChartContainer>
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-center gap-2 sm:gap-4 mt-4">
        {data.map((entry, index) => (
          <div key={index} className="flex items-center gap-2">
            <div
              className="w-3 h-3 rounded-full flex-shrink-0"
              style={{ backgroundColor: entry.color }}
            />
            <span className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
              {entry.name}: {entry.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
} 