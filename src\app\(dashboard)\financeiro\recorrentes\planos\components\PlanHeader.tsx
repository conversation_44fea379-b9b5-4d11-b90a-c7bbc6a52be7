import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { ArrowLeft, Plus } from 'lucide-react'

export function PlanHeader() {
  return (
    <div className="mb-4 sm:mb-6">
      <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <div className="flex-1">
          <div className="flex items-center space-x-4 mb-2">
            <Button asChild variant="ghost" size="sm">
              <Link href="/financeiro/recorrentes">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Voltar
              </Link>
            </Button>
          </div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Planos de Assinatura</h1>
          <p className="text-sm sm:text-base text-muted-foreground mt-1">Configure e gerencie os planos de assinatura oferecidos pela academia.</p>
        </div>
        <div className="flex-shrink-0">
          <Button asChild className="w-full sm:w-auto">
            <Link href="/financeiro/recorrentes/planos/criar">
              <Plus className="h-4 w-4 mr-2" />
              Novo Plano
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}