import Link from 'next/link'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Plus } from 'lucide-react'

export function AddNewPlanCard() {
  return (
    <Card className="shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden group hover:shadow-xl transition-all duration-300 border-2 border-dashed border-gray-300 dark:border-gray-600 flex flex-col items-center justify-center min-h-[250px] sm:min-h-[300px]">
      <CardHeader className="pb-3 sm:pb-4 bg-gradient-to-r from-blue-50/50 to-transparent dark:from-blue-900/20 dark:to-transparent w-full text-center">
        <CardTitle className="text-sm font-semibold text-gray-700 dark:text-gray-300 flex items-center justify-center gap-2">
          <div className="p-1 bg-blue-100 dark:bg-blue-900/30 rounded-full">
            <Plus className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <span className="text-xs sm:text-sm">Criar Novo Plano</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col items-center justify-center flex-1 pt-2 pb-6 sm:pb-8 text-center px-4">
        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-full flex items-center justify-center mb-3 sm:mb-4">
          <Plus className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 font-medium leading-relaxed">Configure um novo plano de assinatura para sua academia</p>
        <Button asChild className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 w-full sm:w-auto">
          <Link href="/financeiro/recorrentes/planos/criar">
            <Plus className="h-4 w-4 mr-2" />
            Novo Plano
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
}