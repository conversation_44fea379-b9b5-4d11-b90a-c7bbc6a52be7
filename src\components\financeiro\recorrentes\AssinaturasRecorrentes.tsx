'use client';

import { useState } from "react";
import Link from "next/link";
import { formatDistance } from "date-fns";
import { ptBR } from "date-fns/locale";
import { utcToBrasilia, nowInBrasilia } from "@/utils/timezone-utils";
import { formatDateBrazil } from "@/utils/format";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import {
  TooltipRoot,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { MoreHorizontal, Play, Pause, RotateCcw, AlertCircle, Users, UserCheck, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { User } from "@/app/(dashboard)/alunos/components/selectusers/types";
import { motion, AnimatePresence } from "framer-motion";
import { TableRowActionAnimation, RowRemovalAnimation } from "@/app/(dashboard)/alunos/components/list/row-action-animation";
import { useRowActionAnimation } from "@/app/(dashboard)/alunos/hooks/use-row-action-animation";
import { BulkActionsDialog } from "./BulkActionsDialog";

type AssinaturaStatus = 'active' | 'paused' | 'canceled' | 'expired';
type PaymentStatus = 'pending' | 'paid' | 'failed' | 'overdue';

interface Assinatura {
  id: string;
  membership_id: string;
  student_id: string; // Este é o user_id da tabela users, não o id da tabela students
  student_name: string;
  student_email: string;
  student_avatar?: string;
  plan_title: string;
  plan_type: 'individual' | 'family' | 'corporate';
  pricing_type?: 'recurring' | 'one-time' | 'per-session' | 'trial';
  amount: number;
  frequency: string;
  frequency_number: number;
  status: AssinaturaStatus;
  start_date: string;
  end_date?: string;
  // NOTE: next_billing_date removed - now using payments table as source of truth
  branch_name?: string;
  created_at: string;
  last_payment_status?: PaymentStatus;
  last_payment_date?: string;
  last_payment_paid_at?: string;
  last_payment_overdue_date?: string;
  next_payment_due?: string;
  failed_attempts?: number;
}

interface AssinaturasRecorrentesProps {
  assinaturas?: Assinatura[];
  isLoading?: boolean;
  isSelecting?: boolean;
  selectedUsers?: User[];
  onSelectUser?: (user: User) => void;
  onToggleSelecting?: () => void;
  onPausar?: (id: string) => void;
  onRetomar?: (id: string) => void;
  onTentarNovamente?: (id: string) => void;
  onRefresh?: () => void;
  onBulkAction?: (action: string, userIds: string[]) => Promise<void>;
}

// Componente de skeleton para a tabela
function AssinaturasTableSkeleton({ isSelecting = false }: { isSelecting?: boolean }) {
  return (
    <div className="space-y-4">
      <div className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <Table>
          <TableHeader>
            <TableRow>
              {isSelecting && (
                <TableHead className="w-12">
                  <Skeleton className="h-4 w-4" />
                </TableHead>
              )}
              <TableHead className="hidden sm:table-cell">Foto</TableHead>
              <TableHead>Aluno</TableHead>
              <TableHead className="hidden lg:table-cell">Plano</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead className="hidden md:table-cell">Frequência</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="hidden xl:table-cell">Último Pagamento</TableHead>
              <TableHead className="hidden lg:table-cell">Próximo Vencimento</TableHead>
              <TableHead className="hidden xl:table-cell">Filial</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, index) => (
              <TableRow key={index}>
                {isSelecting && (
                  <TableCell className="w-12">
                    <div className="h-4 w-4 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                  </TableCell>
                )}
                <TableCell className="hidden sm:table-cell">
                  <div className="h-8 w-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-2">
                    <div className="h-4 w-32 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                    <div className="h-3 w-40 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  <div className="space-y-2">
                    <div className="h-4 w-28 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                    <div className="h-3 w-20 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-4 w-20 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
                <TableCell className="hidden md:table-cell">
                  <div className="h-4 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="h-6 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
                <TableCell className="hidden xl:table-cell">
                  <div className="space-y-2">
                    <div className="h-6 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded-full relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                    <div className="h-3 w-24 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden lg:table-cell">
                  <div className="space-y-2">
                    <div className="h-4 w-20 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                    <div className="h-3 w-16 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="hidden xl:table-cell">
                  <div className="h-4 w-24 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <div className="h-8 w-8 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        </div>
      </div>

      {/* Skeleton dos botões embaixo da tabela */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="h-10 w-40 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <div className="h-10 w-24 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 animate-pulse rounded relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Função utilitária para garantir que datas do tipo 'date' do Supabase sejam sempre interpretadas como meia-noite no horário de Brasília (UTC-3)
function parseDateAsBrasilia(dateStr: string) {
  // dateStr: "2025-08-16"
  const [year, month, day] = dateStr.split('-').map(Number);
  // Cria a data como se fosse meia-noite local
  const localDate = new Date(year, month - 1, day, 0, 0, 0, 0);
  // Offset de Brasília: UTC-3 (em minutos: -180)
  const brasiliaOffset = -3 * 60;
  // Ajusta para o timezone de Brasília
  const utc = localDate.getTime() + (localDate.getTimezoneOffset() * 60000);
  return new Date(utc + (brasiliaOffset * 60000));
}

// Função para formatar data e hora para tooltip
function formatDateTimeForTooltip(dateStr: string) {
  try {
    const date = utcToBrasilia(new Date(dateStr));
    return date.toLocaleString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'America/Sao_Paulo'
    });
  } catch (e) {
    return "Data inválida";
  }
}

export function AssinaturasRecorrentes({
  assinaturas,
  isLoading = false,
  isSelecting = false,
  selectedUsers = [],
  onSelectUser,
  onToggleSelecting,
  onPausar,
  onRetomar,
  onTentarNovamente,
  onRefresh,
  onBulkAction
}: AssinaturasRecorrentesProps) {

  const listaAssinaturas = assinaturas || [];
  const [showBulkActionsDialog, setShowBulkActionsDialog] = useState(false);

  // Hook para animações de ação nas linhas
  const {
    executeActionWithAnimation,
    isUserAnimating,
    isUserRemoving,
    getUserAction,
    removeFromList
  } = useRowActionAnimation();

  // Debug: verificar se o hook está funcionando
  console.log('Hook carregado:', { executeActionWithAnimation, isUserAnimating });

  // Função para gerar as iniciais do nome
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Formatar moeda
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Formatar data relativa usando timezone do Brasil
  const formatRelativeDate = (dateStr: string) => {
    try {
      // Se vier no formato "YYYY-MM-DD", parse como data de Brasília
      let brasiliaDate: Date;
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
        brasiliaDate = parseDateAsBrasilia(dateStr);
      } else {
        // fallback para datas com horário
        const utcDate = new Date(dateStr);
        brasiliaDate = utcToBrasilia(utcDate);
      }
      let nowBrasilia = nowInBrasilia();

      // Arredondar ambos para o início do dia em Brasília
      if (brasiliaDate > nowBrasilia) {
        brasiliaDate = new Date(
          brasiliaDate.getFullYear(),
          brasiliaDate.getMonth(),
          brasiliaDate.getDate(),
          0, 0, 0, 0
        );
        nowBrasilia = new Date(
          nowBrasilia.getFullYear(),
          nowBrasilia.getMonth(),
          nowBrasilia.getDate(),
          0, 0, 0, 0
        );
      }

      return formatDistance(brasiliaDate, nowBrasilia, {
        addSuffix: true,
        locale: ptBR
      });
    } catch (e) {
      return "Data inválida";
    }
  };

  // Função para obter o label do status da assinatura
  const getStatusLabel = (status: AssinaturaStatus) => {
    switch (status) {
      case 'active':
        return 'Ativa';
      case 'paused':
        return 'Pausada';
      case 'canceled':
        return 'Cancelada';
      case 'expired':
        return 'Expirada';
      default:
        return 'Desconhecido';
    }
  };

  // Função para obter a variante do badge baseada no status da assinatura
  const getStatusBadgeVariant = (status: AssinaturaStatus) => {
    switch (status) {
      case 'active':
        return 'statusActive';
      case 'paused':
        return 'statusSuspended';
      case 'canceled':
        return 'statusInactive';
      case 'expired':
        return 'statusInactive';
      default:
        return 'outline';
    }
  };

  // Função para obter o label do status do pagamento
  const getPaymentStatusLabel = (status?: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return 'Pago';
      case 'pending':
        return 'Pendente';
      case 'failed':
        return 'Falha';
      case 'overdue':
        return 'Vencido';
      default:
        return 'N/A';
    }
  };

  // Função para obter a variante do badge baseada no status do pagamento
  const getPaymentStatusBadgeVariant = (status?: PaymentStatus) => {
    switch (status) {
      case 'paid':
        return 'statusActive';
      case 'pending':
        return 'outline';
      case 'failed':
        return 'destructive';
      case 'overdue':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Função para obter o label da frequência
  const getFrequencyLabel = (assinatura: Assinatura) => {
    // Verificar se é um plano único
    if (assinatura.pricing_type === 'one-time') {
      return 'Pagamento único';
    }

    // Verificar outros indicadores de plano único como fallback
    const isOneTime = assinatura.frequency === 'one-time' ||
      assinatura.frequency_number === 0 ||
      !assinatura.next_billing_date;

    if (isOneTime) {
      return 'Pagamento único';
    }

    const frequencyMap: Record<string, string> = {
      'day': 'Diário',
      'week': 'Semanal',
      'month': 'Mensal',
      'year': 'Anual'
    };

    const label = frequencyMap[assinatura.frequency] || assinatura.frequency;
    return assinatura.frequency_number > 1 ? `A cada ${assinatura.frequency_number} ${label.toLowerCase()}s` : label;
  };

  // Converter assinatura para o formato do seletor de usuários
  const mapAssinaturaToUser = (assinatura: Assinatura): User => {
    return {
      id: assinatura.student_id,
      name: assinatura.student_name,
      email: assinatura.student_email,
      avatar: assinatura.student_avatar,
      isActive: assinatura.status === 'active',
      branch: assinatura.branch_name,
    };
  };

  // Verificar se todas as assinaturas estão selecionadas
  const areAllSelected =
    listaAssinaturas.length > 0 &&
    selectedUsers.length === listaAssinaturas.length &&
    listaAssinaturas.every(assinatura =>
      selectedUsers.some(user => user.id === assinatura.student_id)
    );

  // Selecionar ou desselecionar todas as assinaturas
  const toggleSelectAll = () => {
    if (!onSelectUser) return;

    if (areAllSelected) {
      // Desselecionar todas
      listaAssinaturas.forEach(assinatura => {
        const user = mapAssinaturaToUser(assinatura);
        onSelectUser(user);
      });
    } else {
      // Selecionar todas que não estão selecionadas
      listaAssinaturas.forEach(assinatura => {
        const isSelected = selectedUsers.some(user => user.id === assinatura.student_id);
        if (!isSelected) {
          const user = mapAssinaturaToUser(assinatura);
          onSelectUser(user);
        }
      });
    }
  };

  // Funções de ação com animação
  const handlePausar = async (membershipId: string, studentId: string) => {
    if (!onPausar) return;

    console.log('Iniciando animação de pausar para:', studentId);
    console.log('Estado antes da animação:', {
      isAnimating: isUserAnimating(studentId),
      action: getUserAction(studentId)
    });

    try {
      const result = await executeActionWithAnimation(
        studentId,
        'pause',
        async () => {
          console.log('Executando ação de pausar...');
          // Simular delay para ver a animação
          await new Promise(resolve => setTimeout(resolve, 2000));
          onPausar(membershipId);
          return { success: true, message: 'Assinatura pausada com sucesso' };
        }
      );
      console.log('Resultado da animação:', result);
      console.log('Estado após a animação:', {
        isAnimating: isUserAnimating(studentId),
        action: getUserAction(studentId)
      });
    } catch (error) {
      console.error('Erro na animação de pausar:', error);
    }
  };

  const handleRetomar = async (membershipId: string, studentId: string) => {
    if (!onRetomar) return;

    console.log('Iniciando animação de retomar para:', studentId);

    await executeActionWithAnimation(
      studentId,
      'resume',
      async () => {
        console.log('Executando ação de retomar...');
        // Simular delay para ver a animação
        await new Promise(resolve => setTimeout(resolve, 2000));
        onRetomar(membershipId);
        return { success: true, message: 'Assinatura retomada com sucesso' };
      }
    );
  };

  const handleTentarNovamente = async (membershipId: string, studentId: string) => {
    if (!onTentarNovamente) return;

    await executeActionWithAnimation(
      studentId,
      'generic',
      async () => {
        onTentarNovamente(membershipId);
        return { success: true, message: 'Tentativa de cobrança iniciada' };
      }
    );
  };

  // Mostrar skeleton quando estiver carregando
  if (isLoading) {
    return <AssinaturasTableSkeleton isSelecting={isSelecting} />;
  }

  return (
    <TooltipProvider>
      <div className="space-y-4">
      <div className="border-0 shadow-lg bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50 overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <Table>
          <TableHeader>
            <TableRow>
              {isSelecting && (
                <TableHead className="w-12">
                  <Checkbox
                    checked={areAllSelected}
                    onCheckedChange={() => toggleSelectAll()}
                    className="opacity-90"
                  />
                </TableHead>
              )}
              <TableHead className="hidden sm:table-cell">Foto</TableHead>
              <TableHead>Aluno</TableHead>
              <TableHead className="hidden lg:table-cell">Plano</TableHead>
              <TableHead>Valor</TableHead>
              <TableHead className="hidden md:table-cell">Frequência</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="hidden xl:table-cell">Último Pagamento</TableHead>
              <TableHead className="hidden lg:table-cell">Próximo Vencimento</TableHead>
              <TableHead className="hidden xl:table-cell">Filial</TableHead>
              <TableHead className="text-right">Ações</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <AnimatePresence mode="popLayout">
              {listaAssinaturas.map((assinatura) => {
                const isSelected = selectedUsers.some(user => user.id === assinatura.student_id);
                const isAnimating = isUserAnimating(assinatura.student_id);
                const isRemoving = isUserRemoving(assinatura.student_id);
                const currentAction = getUserAction(assinatura.student_id);

                // Se está sendo removido, renderizar animação de remoção
                if (isRemoving) {
                  return (
                    <RowRemovalAnimation
                      key={`${assinatura.id}-removing`}
                      onComplete={() => removeFromList(assinatura.student_id)}
                      colSpan={isSelecting ? 11 : 10}
                    />
                  );
                }

                // Se está animando, renderizar linha de animação
                if (isAnimating && currentAction) {
                  return (
                    <motion.tr
                      key={`${assinatura.id}-animating`}
                      layout
                      initial={{ opacity: 1 }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                      className="relative"
                    >
                      <TableRowActionAnimation
                        action={currentAction}
                        isVisible={isAnimating}
                        colSpan={isSelecting ? 11 : 10}
                      />
                    </motion.tr>
                  );
                }

                return (
                  <motion.tr
                    key={assinatura.id}
                    layout
                    initial={{ opacity: 1 }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.2 }}
                    className="relative"
                  >
                    {isSelecting && (
                      <TableCell className="w-12">
                        <Checkbox
                          checked={isSelected}
                          onClick={() => {
                            if (onSelectUser) {
                              const user = mapAssinaturaToUser(assinatura);
                              onSelectUser(user);
                            }
                          }}
                          className="border-gray-300 dark:border-gray-500"
                        />
                      </TableCell>
                    )}
                    <TableCell className="hidden sm:table-cell">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={assinatura.student_avatar || ""} alt={assinatura.student_name} />
                        <AvatarFallback>{getInitials(assinatura.student_name)}</AvatarFallback>
                      </Avatar>
                    </TableCell>
                    <TableCell className="font-medium">
                      <Link href={`/perfil/${assinatura.student_id}?from=assinaturas`} className="flex flex-col hover:underline">
                        <div className="flex items-center gap-2 sm:hidden">
                          <Avatar className="h-6 w-6">
                            <AvatarImage src={assinatura.student_avatar || ""} alt={assinatura.student_name} />
                            <AvatarFallback className="text-xs">{getInitials(assinatura.student_name)}</AvatarFallback>
                          </Avatar>
                          <span>{assinatura.student_name}</span>
                        </div>
                        <span className="hidden sm:block">{assinatura.student_name}</span>
                        <span className="text-xs text-muted-foreground">
                          {assinatura.student_email}
                        </span>
                        <span className="text-xs text-muted-foreground lg:hidden">
                          {assinatura.plan_title} • {assinatura.plan_type === 'individual' ? 'Individual' :
                            assinatura.plan_type === 'family' ? 'Familiar' :
                              assinatura.plan_type === 'corporate' ? 'Corporativo' :
                                assinatura.plan_type}
                        </span>
                      </Link>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex flex-col">
                        <span className="font-medium">{assinatura.plan_title}</span>
                        <span className="text-xs text-muted-foreground capitalize">
                          {assinatura.plan_type === 'individual' ? 'Individual' :
                            assinatura.plan_type === 'family' ? 'Familiar' :
                              assinatura.plan_type === 'corporate' ? 'Corporativo' :
                                assinatura.plan_type}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{formatCurrency(assinatura.amount)}</span>
                        <span className="text-xs text-muted-foreground md:hidden">
                          {getFrequencyLabel(assinatura)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <span className="text-sm">{getFrequencyLabel(assinatura)}</span>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <Badge variant={getStatusBadgeVariant(assinatura.status)}>
                          {getStatusLabel(assinatura.status)}
                        </Badge>
                        {/* Mostrar informações de pagamento em mobile */}
                        <div className="xl:hidden">
                          {(assinatura.last_payment_status === 'paid' && assinatura.last_payment_paid_at) ? (
                            <TooltipRoot>
                              <TooltipTrigger asChild>
                                <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit cursor-help text-xs">
                                  {getPaymentStatusLabel(assinatura.last_payment_status)}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Pago em: {formatDateTimeForTooltip(assinatura.last_payment_paid_at)}</p>
                              </TooltipContent>
                            </TooltipRoot>
                          ) : (assinatura.last_payment_status === 'overdue' && (assinatura.last_payment_overdue_date || assinatura.next_payment_due)) ? (
                            <TooltipRoot>
                              <TooltipTrigger asChild>
                                <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit cursor-help text-xs">
                                  {getPaymentStatusLabel(assinatura.last_payment_status)}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Vencido em: {formatDateBrazil(assinatura.last_payment_overdue_date || assinatura.next_payment_due!)}</p>
                              </TooltipContent>
                            </TooltipRoot>
                          ) : (
                            <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit text-xs">
                              {getPaymentStatusLabel(assinatura.last_payment_status)}
                            </Badge>
                          )}
                        </div>
                        {/* Mostrar próximo vencimento em mobile */}
                        <div className="lg:hidden">
                          {assinatura.next_payment_due ? (
                            <span className="text-xs text-muted-foreground">
                              Próx: {formatRelativeDate(assinatura.next_payment_due)}
                            </span>
                          ) : (
                            <span className="text-xs text-muted-foreground">
                              {assinatura.pricing_type === 'one-time' ? 'Pagamento único' : 'N/A'}
                            </span>
                          )}
                        </div>
                        {/* Mostrar filial em mobile */}
                        <div className="xl:hidden">
                          <span className="text-xs text-muted-foreground">
                            {assinatura.branch_name || "Não informado"}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      <div className="flex flex-col">
                        {/* Badge com tooltip baseado no status */}
                        {(assinatura.last_payment_status === 'paid' && assinatura.last_payment_paid_at) ? (
                          <TooltipRoot>
                            <TooltipTrigger asChild>
                              <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit cursor-help">
                                {getPaymentStatusLabel(assinatura.last_payment_status)}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Pago em: {formatDateTimeForTooltip(assinatura.last_payment_paid_at)}</p>
                            </TooltipContent>
                          </TooltipRoot>
                        ) : (assinatura.last_payment_status === 'overdue' && (assinatura.last_payment_overdue_date || assinatura.next_payment_due)) ? (
                          <TooltipRoot>
                            <TooltipTrigger asChild>
                              <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit cursor-help">
                                {getPaymentStatusLabel(assinatura.last_payment_status)}
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Vencido em: {formatDateBrazil(assinatura.last_payment_overdue_date || assinatura.next_payment_due!)}</p>
                            </TooltipContent>
                          </TooltipRoot>
                        ) : (
                          <Badge variant={getPaymentStatusBadgeVariant(assinatura.last_payment_status)} className="w-fit">
                            {getPaymentStatusLabel(assinatura.last_payment_status)}
                          </Badge>
                        )}

                        {assinatura.last_payment_date && (
                          <span className="text-xs text-muted-foreground mt-1">
                            {formatRelativeDate(assinatura.last_payment_date)}
                          </span>
                        )}
                        {assinatura.failed_attempts && assinatura.failed_attempts > 0 && (
                          <span className="text-xs text-red-600 mt-1 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {assinatura.failed_attempts} tentativas
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex flex-col">
                        {assinatura.next_payment_due ? (
                          <>
                            <span className="text-sm">
                              {formatRelativeDate(assinatura.next_payment_due)}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {formatDateBrazil(assinatura.next_payment_due)}
                            </span>
                          </>
                        ) : (
                          <span className="text-sm text-muted-foreground">
                            {assinatura.pricing_type === 'one-time' ? 'Pagamento único' : 'N/A'}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      <span className="text-sm">{assinatura.branch_name || "Não informado"}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0"
                          >
                            <span className="sr-only">Abrir menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/perfil/${assinatura.student_id}?from=assinaturas`}>
                              Ver perfil do aluno
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {assinatura.status === 'active' && (
                            <DropdownMenuItem
                              onClick={() => handlePausar(assinatura.membership_id, assinatura.student_id)}
                              className="cursor-pointer"
                            >
                              <Pause className="h-4 w-4 mr-2" />
                              Pausar assinatura
                            </DropdownMenuItem>
                          )}
                          {assinatura.status === 'paused' && (
                            <DropdownMenuItem
                              onClick={() => handleRetomar(assinatura.membership_id, assinatura.student_id)}
                              className="cursor-pointer"
                            >
                              <Play className="h-4 w-4 mr-2" />
                              Retomar assinatura
                            </DropdownMenuItem>
                          )}
                          {assinatura.last_payment_status === 'failed' && (
                            <DropdownMenuItem
                              onClick={() => handleTentarNovamente(assinatura.membership_id, assinatura.student_id)}
                              className="cursor-pointer"
                            >
                              <RotateCcw className="h-4 w-4 mr-2" />
                              Tentar cobrança novamente
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </motion.tr>
                );
              })}
            </AnimatePresence>
          </TableBody>
        </Table>
        </div>
      </div>

      {/* Botão de seleção embaixo da tabela */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
          <Button
            variant={isSelecting ? "default" : "outline"}
            onClick={onToggleSelecting}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            {isSelecting ? (
              <>
                <UserCheck className="h-4 w-4" />
                <span className="hidden sm:inline">Cancelar Seleção</span>
                <span className="sm:hidden">Cancelar</span>
              </>
            ) : (
              <>
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Selecionar Assinaturas</span>
                <span className="sm:hidden">Selecionar</span>
              </>
            )}
          </Button>

          {isSelecting && selectedUsers.length > 0 && (
            <>
              <span className="text-sm text-muted-foreground">
                {selectedUsers.length} selecionado{selectedUsers.length > 1 ? 's' : ''}
              </span>
              <Button
                variant="secondary"
                onClick={() => setShowBulkActionsDialog(true)}
                className="flex items-center gap-2 w-full sm:w-auto"
              >
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Ações em Massa</span>
                <span className="sm:hidden">Ações</span>
              </Button>
            </>
          )}
        </div>

        <div className="flex items-center gap-2 w-full sm:w-auto">
          <Button
            variant="outline"
            onClick={onRefresh}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <RefreshCw className="h-4 w-4" />
            <span className="hidden sm:inline">Atualizar</span>
          </Button>
        </div>
      </div>

      {/* Dialog de ações em massa */}
      <BulkActionsDialog
        isOpen={showBulkActionsDialog}
        onClose={() => setShowBulkActionsDialog(false)}
        selectedUsers={selectedUsers}
        onBulkAction={onBulkAction || (async () => {})}
      />
      </div>
    </TooltipProvider>
  );
}
