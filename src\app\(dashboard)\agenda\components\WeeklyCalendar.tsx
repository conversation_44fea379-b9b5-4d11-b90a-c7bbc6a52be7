'use client'

import React, { useState, useEffect } from 'react'
import { format, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { Calendar as CalendarIcon, AlertCircle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { useCalendar } from '../contexts/CalendarContext'
import { CalendarHeader } from './CalendarHeader'
import { DayColumn } from './DayColumn'
import { EventModal } from './EventModal'
import { cn } from '@/lib/utils'

export function WeeklyCalendar() {
  const {
    currentDate,
    events,
    isLoading,
    error,
    getTimeSlots,
    refreshEvents
  } = useCalendar()

  const [currentTime, setCurrentTime] = useState(new Date())

  // Atualizar a hora atual a cada minuto
  useEffect(() => {
    const updateCurrentTime = () => {
      setCurrentTime(new Date())
    }

    updateCurrentTime()
    const interval = setInterval(updateCurrentTime, 60000)
    return () => clearInterval(interval)
  }, [])

  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 }) // Começa na segunda
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 })
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd })

  // Usar horários fixos de 24 horas
  const timeSlots = getTimeSlots()

  const getEventsForDay = (day: Date) => {
    return events.filter(event => isSameDay(event.startTime, day))
  }

  // Verificar se há algum dia da semana atual que é hoje
  const hasToday = weekDays.some(day => isSameDay(day, new Date()))

  // Altura fixa para cada slot (48px) com 24 slots = 1152px total
  const SLOT_HEIGHT = 48
  const gridHeight = timeSlots.length * SLOT_HEIGHT

  const handleRetry = async () => {
    await refreshEvents()
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header com navegação */}
      <CalendarHeader />

      {/* Exibir erro se houver */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <span className="text-sm">{error}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              disabled={isLoading}
              className="w-full sm:w-auto sm:ml-4"
            >
              <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
              Tentar novamente
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Calendário */}
      <Card className="overflow-hidden shadow-sm">
        <div className="bg-background border-b border-border/50">
          {/* Grid Header - Responsivo */}
          <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 border-b border-border/50">
            {/* Coluna vazia para os horários */}
            <div className="p-2 sm:p-4 bg-muted/20 border-r border-border/50">
              <span className="text-xs sm:text-xs text-muted-foreground font-semibold uppercase tracking-wide">
                <span className="hidden sm:inline">Horário</span>
                <span className="sm:hidden">Hr</span>
              </span>
            </div>

            {/* Colunas dos dias - Responsivo */}
            {weekDays.map((day, index) => {
              // Ocultar dias extras em mobile usando CSS responsivo
              const isHiddenOnMobile = index >= 3
              const isHiddenOnTablet = index >= 5
              const dayEvents = getEventsForDay(day)
              const isCurrentDay = isSameDay(day, new Date())

              return (
                <div
                  key={day.toISOString()}
                  className={cn(
                    "p-2 sm:p-4 text-center border-r border-border/50 last:border-r-0 transition-colors",
                    "hover:bg-muted/30 touch-manipulation",
                    isCurrentDay && "bg-primary/5 border-primary/20",
                    // Ocultar colunas extras em mobile/tablet
                    isHiddenOnMobile && "hidden sm:block",
                    isHiddenOnTablet && "hidden lg:block"
                  )}
                >
                  <div className="space-y-1">
                    <div className={cn(
                      "text-xs sm:text-sm font-semibold capitalize",
                      isCurrentDay ? "text-primary" : "text-foreground"
                    )}>
                      <span className="hidden sm:inline">{format(day, 'EEEE', { locale: ptBR })}</span>
                      <span className="sm:hidden">{format(day, 'EEE', { locale: ptBR })}</span>
                    </div>
                    <div className={cn(
                      "text-xs sm:text-xs",
                      isCurrentDay ? "text-primary font-bold" : "text-muted-foreground"
                    )}>
                      {format(day, 'dd/MM')}
                    </div>
                    {/* Contador de eventos */}
                    {dayEvents.length > 0 && (
                      <div className={cn(
                        "inline-flex items-center justify-center w-4 h-4 sm:w-5 sm:h-5 rounded-full text-xs font-bold",
                        isCurrentDay
                          ? "bg-primary text-primary-foreground"
                          : "bg-muted-foreground/20 text-muted-foreground"
                      )}>
                        {dayEvents.length}
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Grid Body com scroll */}
        <div className="bg-background overflow-y-auto overflow-x-auto sm:overflow-x-visible agenda-scroll-container">
          {isLoading ? (
            <div className="flex items-center justify-center h-64 sm:h-96">
              <div className="flex flex-col items-center gap-3">
                <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-primary"></div>
                <p className="text-xs sm:text-sm text-muted-foreground">Carregando eventos...</p>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64 sm:h-96">
              <div className="flex flex-col items-center gap-3 text-center px-4">
                <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground/50" />
                <div className="space-y-2">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">
                    Não foi possível carregar os eventos
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRetry}
                    disabled={isLoading}
                    className="w-full sm:w-auto"
                  >
                    <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
                    Tentar novamente
                  </Button>
                </div>
              </div>
            </div>
          ) : events.length === 0 ? (
            <div className="flex items-center justify-center h-64 sm:h-96">
              <div className="flex flex-col items-center gap-3 text-center px-4">
                <CalendarIcon className="h-8 w-8 sm:h-12 sm:w-12 text-muted-foreground/50" />
                <div className="space-y-1">
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">
                    Nenhum evento encontrado
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Não há aulas agendadas para esta semana
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-4 sm:grid-cols-6 lg:grid-cols-8 min-w-full agenda-mobile-scroll" style={{ minHeight: `${gridHeight}px` }}>
              {/* Coluna de horários */}
              <div className="sticky left-0 border-r border-border/50 bg-muted/10 z-10">
                {timeSlots.map((slot, index) => {
                  const isCurrentHour = hasToday && slot.hour === currentTime.getHours()

                  return (
                    <div
                      key={slot.time}
                      className={cn(
                        "px-1 sm:px-3 py-2 border-b border-border/30 flex items-start justify-end relative",
                        "hover:bg-muted/20 transition-colors",
                        index === timeSlots.length - 1 && "border-b-0",
                        // Destacar horários comerciais
                        slot.hour >= 6 && slot.hour <= 22 ? "bg-background" : "bg-muted/30",
                        // Destacar hora atual
                        isCurrentHour && "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800/30"
                      )}
                      style={{ height: `${SLOT_HEIGHT}px` }}
                    >
                      <span className={cn(
                        "text-xs font-medium",
                        // Destacar horários de pico
                        (slot.hour >= 6 && slot.hour <= 8) || (slot.hour >= 18 && slot.hour <= 22)
                          ? "text-primary font-semibold"
                          : slot.hour >= 6 && slot.hour <= 22
                          ? "text-foreground"
                          : "text-muted-foreground",
                        // Destacar hora atual
                        isCurrentHour && "text-red-600 dark:text-red-400 font-bold"
                      )}>
                        {slot.time}
                      </span>

                      {/* Indicador visual para a hora atual */}
                      {isCurrentHour && (
                        <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500 current-time-line" />
                      )}
                    </div>
                  )
                })}
              </div>

              {/* Colunas dos dias */}
              {weekDays.map((day, index) => {
                const isHiddenOnMobile = index >= 3
                const isHiddenOnTablet = index >= 5

                return (
                  <div
                    key={day.toISOString()}
                    className={cn(
                      isHiddenOnMobile && "hidden sm:block",
                      isHiddenOnTablet && "hidden lg:block"
                    )}
                  >
                    <DayColumn
                      day={day}
                      events={getEventsForDay(day)}
                      timeSlots={timeSlots}
                    />
                  </div>
                )
              })}
            </div>
          )}
        </div>

        {/* Rodapé com informações sobre os horários */}
        {!isLoading && !error && timeSlots.length > 0 && (
          <div className="bg-muted/20 border-t border-border/50 px-2 sm:px-4 py-2">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 text-xs text-muted-foreground">
              <span className="text-center sm:text-left">
                <span className="hidden sm:inline">Horários: {timeSlots[0]?.time} - {timeSlots[timeSlots.length - 1]?.time} (24h)</span>
                <span className="sm:hidden">{timeSlots[0]?.time} - {timeSlots[timeSlots.length - 1]?.time}</span>
              </span>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-center sm:text-right">
                <span>
                  <span className="hidden sm:inline">{timeSlots.length} slots de horário • </span>
                  {events.length} evento{events.length !== 1 ? 's' : ''} na semana
                </span>
                {hasToday && (
                  <span className="flex items-center justify-center sm:justify-start gap-1 text-red-600 dark:text-red-400 font-medium">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    <span className="hidden sm:inline">Hora atual: </span>
                    {format(currentTime, 'HH:mm')}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Modal de eventos */}
      <EventModal />
    </div>
  )
} 