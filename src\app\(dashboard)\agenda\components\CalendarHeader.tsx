'use client'

import React from 'react'
import { format, startOfWeek, endOfWeek, isSameWeek } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useCalendar } from '../contexts/CalendarContext'
import { AgendaFilters } from './AgendaFilters'
import { cn } from '@/lib/utils'

export function CalendarHeader() {
  const { 
    currentDate, 
    events,
    nextWeek,
    prevWeek,
    goToToday
  } = useCalendar()

  const weekStart = startOfWeek(currentDate, { weekStartsOn: 1 })
  const weekEnd = endOfWeek(currentDate, { weekStartsOn: 1 })
  const isCurrentWeek = isSameWeek(new Date(), currentDate, { weekStartsOn: 1 })

  // Contar eventos da semana atual
  const weekEvents = events.filter(event => 
    event.startTime >= weekStart && event.startTime <= weekEnd
  )

  const eventsByStatus = {
    pending: weekEvents.filter(e => e.status === 'pending').length,
    ongoing: weekEvents.filter(e => e.status === 'ongoing').length,
    completed: weekEvents.filter(e => e.status === 'completed').length,
    cancelled: weekEvents.filter(e => e.status === 'cancelled').length,
  }

  const eventsByType = {
    class: weekEvents.filter(e => e.type === 'class').length,
    appointment: weekEvents.filter(e => e.type === 'appointment').length,
    event: weekEvents.filter(e => e.type === 'event').length,
  }

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      {/* Título e informações da semana */}
      <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6">
        <div className="space-y-2">
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
            <h2 className="text-lg sm:text-xl font-semibold text-foreground">
              {format(currentDate, "MMMM 'de' yyyy", { locale: ptBR })}
            </h2>
            {isCurrentWeek && (
              <Badge variant="default" className="text-xs w-fit">
                <Clock className="h-3 w-3 mr-1" />
                Semana Atual
              </Badge>
            )}
          </div>
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
            <p className="text-xs sm:text-sm text-muted-foreground">
              Semana de {format(weekStart, "dd/MM", { locale: ptBR })} até {format(weekEnd, "dd/MM", { locale: ptBR })}
            </p>

            {/* Resumo de eventos */}
            {weekEvents.length > 0 && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <div className="flex items-center gap-2">
                  <span className="text-xs text-muted-foreground hidden sm:inline">•</span>
                  <span className="text-xs font-medium text-foreground">
                    {weekEvents.length} evento{weekEvents.length !== 1 ? 's' : ''}
                  </span>
                </div>

                {/* Badges de status (apenas os que existem) */}
                <div className="flex gap-1 flex-wrap">
                  {eventsByStatus.pending > 0 && (
                    <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                      <span className="hidden sm:inline">{eventsByStatus.pending} agendado{eventsByStatus.pending !== 1 ? 's' : ''}</span>
                      <span className="sm:hidden">{eventsByStatus.pending} agend.</span>
                    </Badge>
                  )}
                  {eventsByStatus.ongoing > 0 && (
                    <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                      <span className="hidden sm:inline">{eventsByStatus.ongoing} em andamento{eventsByStatus.ongoing !== 1 ? 's' : ''}</span>
                      <span className="sm:hidden">{eventsByStatus.ongoing} ativo{eventsByStatus.ongoing !== 1 ? 's' : ''}</span>
                    </Badge>
                  )}
                  {eventsByStatus.completed > 0 && (
                    <Badge variant="outline" className="text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      <span className="hidden sm:inline">{eventsByStatus.completed} concluído{eventsByStatus.completed !== 1 ? 's' : ''}</span>
                      <span className="sm:hidden">{eventsByStatus.completed} ok</span>
                    </Badge>
                  )}
                  {eventsByStatus.cancelled > 0 && (
                    <Badge variant="destructive" className="text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                      <span className="hidden sm:inline">{eventsByStatus.cancelled} cancelado{eventsByStatus.cancelled !== 1 ? 's' : ''}</span>
                      <span className="sm:hidden">{eventsByStatus.cancelled} canc.</span>
                    </Badge>
                  )}
                </div>

                {/* Separador e badges de tipo */}
                {(eventsByType.class > 0 || eventsByType.appointment > 0 || eventsByType.event > 0) && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-muted-foreground hidden sm:inline">•</span>
                    <div className="flex gap-1 flex-wrap">
                      {eventsByType.class > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <span className="hidden sm:inline">{eventsByType.class} aula{eventsByType.class !== 1 ? 's' : ''}</span>
                          <span className="sm:hidden">{eventsByType.class} aula{eventsByType.class !== 1 ? 's' : ''}</span>
                        </Badge>
                      )}
                      {eventsByType.appointment > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <span className="hidden sm:inline">{eventsByType.appointment} atendimento{eventsByType.appointment !== 1 ? 's' : ''}</span>
                          <span className="sm:hidden">{eventsByType.appointment} atend.</span>
                        </Badge>
                      )}
                      {eventsByType.event > 0 && (
                        <Badge variant="outline" className="text-xs">
                          {eventsByType.event} evento{eventsByType.event !== 1 ? 's' : ''}
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Controles de navegação e filtros */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
        {/* Filtros */}
        <div className="order-2 sm:order-1">
          <AgendaFilters />
        </div>

        {/* Controles de navegação */}
        <div className="flex items-center gap-2 order-1 sm:order-2">
          <Button
            variant="outline"
            size="sm"
            onClick={goToToday}
            className={cn(
              "text-xs sm:text-sm transition-all flex-1 sm:flex-none touch-manipulation",
              isCurrentWeek && "bg-primary/10 border-primary/30 text-primary"
            )}
          >
            <CalendarIcon className="h-4 w-4 mr-1 sm:mr-2" />
            <span className="hidden sm:inline">Hoje</span>
            <span className="sm:hidden">Hoje</span>
          </Button>

          <div className="flex items-center border rounded-lg overflow-hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={prevWeek}
              className="h-9 w-9 p-0 rounded-none border-r hover:bg-muted/50 touch-manipulation"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Semana anterior</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextWeek}
              className="h-9 w-9 p-0 rounded-none hover:bg-muted/50 touch-manipulation"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Próxima semana</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 