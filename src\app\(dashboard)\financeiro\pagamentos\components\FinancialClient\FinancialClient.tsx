'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { FinancialTab, FinancialClientProps, FinancialFilterState, PaymentMethodOption, ExpenseCategory, IncomeTransaction, ExpenseTransaction } from '../types';
import { FinancialTabs } from '../FinancialTabs';
import { FinancialMetrics } from '../FinancialMetrics';
import { FinancialFilters } from '../FinancialFilters';
import { TransactionsList } from '../TransactionsList';
import { ExpenseModal } from '../ExpenseModal';
import { useFinancialFilters } from '@/hooks/financeiro/use-financial-filters';
import { useFinancialSearch } from '@/hooks/financeiro/use-financial-search';

interface ExtendedFinancialClientProps extends FinancialClientProps {
  paymentMethods?: PaymentMethodOption[];
  expenseCategories?: ExpenseCategory[];
}

/**
 * Obter label do tipo de pagamento
 */
function getPaymentTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    recurring: 'Recorrente',
    signup_fee: 'Taxa de Matrícula',
    graduation_fee: 'Taxa de Graduação',
    late_fee: 'Multa por Atraso',
    cancellation_fee: 'Taxa de Cancelamento',
    initial_payment: 'Pagamento Único'
  };
  return labels[type] || type;
}

/**
 * Obter label do método de pagamento
 */
function getPaymentMethodLabel(method: string | null): string {
  if (!method) return 'Não definido';

  const labels: Record<string, string> = {
    pix: 'PIX',
    credit_card: 'Cartão de Crédito',
    debit_card: 'Cartão de Débito',
    cash: 'Dinheiro',
    bank_transfer: 'Transferência Bancária'
  };
  return labels[method] || method;
}

/**
 * Obter label do status
 */
function getStatusLabel(status: string): string {
  const labels: Record<string, string> = {
    pending: 'Pendente',
    paid: 'Pago',
    overdue: 'Vencido',
    canceled: 'Cancelado',
    awaiting_confirmation: 'Aguardando Confirmação'
  };
  return labels[status] || status;
}

/**
 * Formatar data para exibição (timezone do Brasil)
 */
function formatDate(dateString: string): string {
  if (!dateString) return 'Data não disponível';

  // Se for apenas uma data (YYYY-MM-DD), adicionar horário para evitar problemas de timezone
  const dateToFormat = dateString.includes('T') ? dateString : `${dateString}T12:00:00.000Z`;

  const date = new Date(dateToFormat);

  // Verificar se a data é válida
  if (isNaN(date.getTime())) {
    return 'Data inválida';
  }

  return date.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Formatar valor monetário para exibição
 */
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

// Função para converter pagamentos em transações de receita
const convertPaymentsToIncomeTransactions = (payments: any[]): IncomeTransaction[] => {
  return payments.map((payment, index) => ({
    id: payment.id || index.toString(), // Usar ID do payment ou índice como fallback
    type: getPaymentTypeLabel(payment.payment_type),
    paymentMethod: getPaymentMethodLabel(payment.payment_method),
    date: formatDate(payment.due_date || payment.created_at), // Usar due_date se disponível, senão created_at
    amount: formatCurrency(parseFloat(payment.amount)),
    status: getStatusLabel(payment.status),
    paidAt: payment.paid_at ? formatDate(payment.paid_at) : undefined, // Data de pagamento
    dueDate: payment.due_date ? formatDate(payment.due_date) : undefined, // Data de vencimento
    description: payment.description || undefined,
    transactionType: 'income' as const,
    studentName: payment.students?.users?.full_name || 'Nome não disponível',
    studentUserId: payment.students?.user_id || undefined,
  }));
};

// Função para converter despesas em transações de despesa
const convertExpensesToExpenseTransactions = (expenses: any[]): ExpenseTransaction[] => {
  return expenses.map((expense, index) => ({
    id: expense.id || index.toString(),
    type: expense.description || 'Despesa',
    paymentMethod: expense.payment_methods?.name || 'Não definido',
    date: formatDate(expense.due_date || expense.created_at),
    amount: formatCurrency(parseFloat(expense.amount)),
    status: getStatusLabel(expense.status),
    paidAt: expense.paid_at ? formatDate(expense.paid_at) : undefined,
    dueDate: expense.due_date ? formatDate(expense.due_date) : undefined,
    description: expense.description || undefined,
    transactionType: 'expense' as const,
    supplierName: expense.supplier_name,
    categoryName: expense.expense_categories?.name || 'Sem categoria',
    categoryColor: expense.expense_categories?.color || '#6B7280',
  }));
};

export function FinancialClient({
  initialTab = 'income',
  incomeMetrics,
  incomeTransactions,
  expenseMetrics = [],
  expenseTransactions = [],
  hasMoreIncome = false,
  hasMoreExpenses = false,
  paymentMethods = [],
  expenseCategories = [],
}: ExtendedFinancialClientProps) {
  const searchParams = useSearchParams();

  // Ler tab inicial dos URL params ou usar o valor padrão
  const getInitialTab = (): FinancialTab => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'income' || tabParam === 'expense') {
      return tabParam;
    }
    return initialTab;
  };

  const [activeTab, setActiveTab] = useState<FinancialTab>(getInitialTab());
  const [isLoading, setIsLoading] = useState(false);

  // Estados do modal de despesas
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false);
  const [selectedExpenseId, setSelectedExpenseId] = useState<string | null>(null);

  // Estados para gerenciar as transações dinamicamente
  const [currentIncomeTransactions, setCurrentIncomeTransactions] = useState<IncomeTransaction[]>(incomeTransactions);
  const [currentExpenseTransactions, setCurrentExpenseTransactions] = useState<ExpenseTransaction[]>(expenseTransactions);
  const [currentHasMoreIncome, setCurrentHasMoreIncome] = useState(hasMoreIncome);
  const [currentHasMoreExpenses, setCurrentHasMoreExpenses] = useState(hasMoreExpenses);

  // Hooks de filtros e busca
  const { filters, updateFilters, clearFilters, switchTransactionType } = useFinancialFilters(activeTab);
  const { searchText, performSearch, updateSearchText } = useFinancialSearch();

  // Sincronizar tab ativa com URL params
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'income' || tabParam === 'expense') {
      if (tabParam !== activeTab) {
        setActiveTab(tabParam);
        switchTransactionType(tabParam);
      }
    }
  }, [searchParams, activeTab, switchTransactionType]);

  // Dados baseados na tab ativa
  const currentMetrics = activeTab === 'income' ? incomeMetrics : expenseMetrics;
  const currentTransactions = activeTab === 'income' ? currentIncomeTransactions : currentExpenseTransactions;
  const hasMore = activeTab === 'income' ? currentHasMoreIncome : currentHasMoreExpenses;

  // Resetar transações quando a tab muda ou dados iniciais mudam
  useEffect(() => {
    if (activeTab === 'income') {
      setCurrentIncomeTransactions(incomeTransactions);
      setCurrentHasMoreIncome(hasMoreIncome);
    } else {
      setCurrentExpenseTransactions(expenseTransactions);
      setCurrentHasMoreExpenses(hasMoreExpenses);
    }
  }, [activeTab, incomeTransactions, expenseTransactions, hasMoreIncome, hasMoreExpenses]);

  const handleTabChange = (tab: FinancialTab) => {
    setActiveTab(tab);
    switchTransactionType(tab);
  };

  const handleLoadMore = async () => {
    setIsLoading(true);
    try {
      const result = await performSearch(searchText, currentTransactions.length, filters, activeTab);
      if (result.success && result.data) {
        if (activeTab === 'income') {
          const newTransactions = convertPaymentsToIncomeTransactions(result.data);
          setCurrentIncomeTransactions(prev => [...prev, ...newTransactions]);
          setCurrentHasMoreIncome(result.hasMore || false);
        } else {
          // Para despesas
          const newTransactions = convertExpensesToExpenseTransactions(result.data);
          setCurrentExpenseTransactions(prev => [...prev, ...newTransactions]);
          setCurrentHasMoreExpenses(result.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Erro ao carregar mais transações:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async (searchTerm: string) => {
    updateSearchText(searchTerm);
    try {
      const result = await performSearch(searchTerm, 0, filters, activeTab);
      if (result.success && result.data) {
        if (activeTab === 'income') {
          const newTransactions = convertPaymentsToIncomeTransactions(result.data);
          setCurrentIncomeTransactions(newTransactions);
          setCurrentHasMoreIncome(result.hasMore || false);
        } else {
          // Para despesas
          const newTransactions = convertExpensesToExpenseTransactions(result.data);
          setCurrentExpenseTransactions(newTransactions);
          setCurrentHasMoreExpenses(result.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Erro na busca:', error);
    }
  };

  const handleFilterChange = async (newFilters: FinancialFilterState) => {
    updateFilters(newFilters);
    setIsLoading(true);
    try {
      const result = await performSearch(searchText, 0, newFilters, activeTab);
      if (result.success && result.data) {
        if (activeTab === 'income') {
          const newTransactions = convertPaymentsToIncomeTransactions(result.data);
          setCurrentIncomeTransactions(newTransactions);
          setCurrentHasMoreIncome(result.hasMore || false);
        } else {
          // Para despesas
          const newTransactions = convertExpensesToExpenseTransactions(result.data);
          setCurrentExpenseTransactions(newTransactions);
          setCurrentHasMoreExpenses(result.hasMore || false);
        }
      }
    } catch (error) {
      console.error('Erro ao aplicar filtros:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = () => {
    // TODO: Implementar exportação
    console.log(`Exportar ${activeTab === 'income' ? 'receitas' : 'despesas'}`);
  };

  const handleExpenseClick = (expenseId: string) => {
    setSelectedExpenseId(expenseId);
    setIsExpenseModalOpen(true);
  };

  const handleExpenseModalClose = () => {
    setIsExpenseModalOpen(false);
    setSelectedExpenseId(null);
  };

  const handleExpenseUpdated = () => {
    // Recarregar as despesas quando uma despesa for atualizada
    if (activeTab === 'expense') {
      handleSearch(searchText);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 px-2 sm:px-0">
      {/* Tabs de navegação */}
      <FinancialTabs activeTab={activeTab} onTabChange={handleTabChange} />

      {/* Métricas */}
      <FinancialMetrics metrics={currentMetrics} />

      {/* Filtros */}
      <FinancialFilters
        transactionType={activeTab}
        onSearchChange={handleSearch}
        onExportClick={handleExport}
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={clearFilters}
        paymentMethods={paymentMethods}
        expenseCategories={expenseCategories}
      />

      {/* Lista de transações */}
      <TransactionsList
        transactions={currentTransactions}
        onLoadMore={handleLoadMore}
        isLoading={isLoading}
        hasMore={hasMore}
        transactionType={activeTab}
        onExpenseClick={handleExpenseClick}
      />

      {/* Modal de despesas */}
      <ExpenseModal
        isOpen={isExpenseModalOpen}
        onClose={handleExpenseModalClose}
        expenseId={selectedExpenseId}
        onExpenseUpdated={handleExpenseUpdated}
      />
    </div>
  );
}
