/* Estilos específicos para a agenda - responsividade mobile */

/* Melhorar scroll horizontal em mobile */
.agenda-scroll-container {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.agenda-scroll-container::-webkit-scrollbar {
  height: 4px;
}

.agenda-scroll-container::-webkit-scrollbar-track {
  background: transparent;
}

.agenda-scroll-container::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.agenda-scroll-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* <PERSON>ha de tempo atual */
.current-time-line {
  background: linear-gradient(90deg, #ef4444, #dc2626);
  box-shadow: 0 0 4px rgba(239, 68, 68, 0.5);
  animation: pulse-line 2s ease-in-out infinite;
}

.current-time-indicator {
  background: #ef4444;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
  animation: pulse-indicator 2s ease-in-out infinite;
}

.current-time-label {
  background: #ef4444;
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: pulse-label 2s ease-in-out infinite;
}

@keyframes pulse-line {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes pulse-indicator {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes pulse-label {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.9; }
}

/* Melhorar touch targets em mobile */
@media (max-width: 640px) {
  .calendar-event-block {
    min-height: 36px !important;
    font-size: 0.75rem;
  }
  
  .calendar-grid-cell {
    min-height: 48px;
  }
  
  /* Melhorar área de toque para botões */
  .touch-manipulation {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Otimizar scroll em mobile */
  .agenda-mobile-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-snap-type: x proximity;
  }
  
  .agenda-mobile-scroll > * {
    scroll-snap-align: start;
  }
}

/* Melhorar contraste em modo escuro */
@media (prefers-color-scheme: dark) {
  .current-time-line {
    box-shadow: 0 0 6px rgba(239, 68, 68, 0.8);
  }
  
  .current-time-indicator {
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.8);
  }
}

/* Animações suaves para transições */
.calendar-event-block {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.calendar-event-block:active {
  transform: scale(0.98);
}

/* Melhorar legibilidade em telas pequenas */
@media (max-width: 480px) {
  .calendar-event-block {
    padding: 4px 6px !important;
  }
  
  .calendar-event-block .text-xs {
    font-size: 0.7rem;
  }
}
