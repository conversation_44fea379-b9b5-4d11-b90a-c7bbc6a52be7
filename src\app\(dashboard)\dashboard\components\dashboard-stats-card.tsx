'use client'

import { Card, CardContent } from "@/components/ui/card";
import { Line<PERSON><PERSON>, Line, XAxis } from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from '@/components/ui/chart';
import clsx from "clsx";

interface ChartData {
  name: string;
  value: number;
}

interface DashboardStatsCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon?: React.ReactNode;
  chartData?: ChartData[];
}

export function DashboardStatsCard({ title, value, change, icon, chartData = [] }: DashboardStatsCardProps) {
  const formattedChange = change !== undefined
    ? `${change >= 0 ? '↑' : '↓'} ${Math.abs(change).toFixed(1)}%`
    : null;

  const changeColor = change !== undefined
    ? (change >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400')
    : '';

  const chartConfig: ChartConfig = {
    value: { label: title, color: '#4A90E2' },
  };

  return (
    <Card className="bg-white dark:bg-gray-800/50 border border-slate-200 dark:border-gray-700/80 text-slate-900 dark:text-white shadow-lg rounded-xl">
      <CardContent className="p-3 sm:p-4">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 mb-2">
          <div className="flex-1 min-w-0">
            <p className="text-xs sm:text-sm text-slate-500 dark:text-gray-400 truncate">{title}</p>
            <p className="text-xl sm:text-2xl lg:text-3xl font-bold break-words">{value}</p>
          </div>
          {formattedChange && (
            <div className={clsx("flex items-center text-xs sm:text-sm font-semibold flex-shrink-0", changeColor)}>
              {formattedChange}
            </div>
          )}
        </div>
        <div className="h-16 sm:h-20 w-full">
          {chartData.length > 0 && (
            <ChartContainer config={chartConfig} className="w-full h-16 sm:h-20">
              <LineChart data={chartData} margin={{ top: 8, right: 0, left: 0, bottom: 0 }}>
                <ChartTooltip content={<ChartTooltipContent />} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="var(--color-value)"
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 3, fill: 'var(--color-value)', stroke: 'white', strokeWidth: 2 }}
                />
                <XAxis dataKey="name" hide />
              </LineChart>
            </ChartContainer>
          )}
        </div>
        {icon && (
          <div className="flex items-center text-xs sm:text-sm text-gray-400 mt-2">
            {icon}
          </div>
        )}
      </CardContent>
    </Card>
  );
} 