'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Plus, Package } from 'lucide-react';

interface PageHeaderProps {
  title: string;
  description: string;
}

export function PageHeader({ title, description }: PageHeaderProps) {
  return (
    <div className="mb-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">{title}</h1>
          <p className="text-muted-foreground text-sm sm:text-base">{description}</p>
        </div>
        <div className="flex space-x-3 w-full sm:w-auto">
          <Button asChild variant="outline" className="w-full sm:w-auto">
            <Link href="/financeiro/recorrentes/planos">
              <Package className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Gerenciar Planos</span>
              <span className="sm:hidden">Planos</span>
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
